# Readme.md - Správa Medium.js pomocí PM2

## Instalace PM2

PM2 je správce procesů pro Node.js aplikace, který umožňuje snadné spouštění aplikací na pozadí.

```shell script
# Globální instalace PM2
npx install pm2
```


## Základní příkazy

### Spuštění aplikace

```shell script
# Spuštění s 4 instancemi
npx pm2 start medium.js --name "medium-worker" -i 4

# Spuštění s 10 instancemi
npx pm2 start medium.js --name "medium-worker" -i 10

# Spuštění přímo přes npx
npx pm2 start medium.js --name "medium-worker" -i 4
```


### Monitoring procesů

```shell script
# Zobrazení všech běžících procesů
npx pm2 ls

# Alternativně
npx pm2 status
```


### Správa logů

```shell script
# Zobrazení logů všech procesů
npx pm2 logs

# Zobrazení logů konkrétního procesu (podle ID)
npx pm2 logs 1
```


### Zastavení a mazání procesů

```shell script
# Zastavení všech procesů
npx pm2 stop all

# Smazání procesu podle názvu
npx pm2 delete medium-worker
```


### Nápověda a příklady

```shell script
# Zobrazení nápovědy
npx pm2 help

# Zobrazení příkladů použití
npx pm2 examples
```


## Spouštění bez PM2

Pokud chcete spustit aplikaci přímo bez správce procesů:

```shell script
node medium.js
```


## Poznámky

- Použití `npx pm2` je vhodné, pokud nemáte PM2 nainstalovaný globálně
- Pro produkční prostředí doporučujeme globální instalaci pomocí `npm install -g pm2`
- Parametr `-i X` určuje počet instancí, které budou spuštěny
- Přidělení názvu pomocí `--name "název"` usnadňuje pozdější správu procesů

## Ukončení a restart

Pro restart nebo úplné ukončení všech PM2 procesů:

```shell script
# Restart všech procesů
pm2 restart all

# Úplné ukončení PM2 démona
pm2 kill
```
