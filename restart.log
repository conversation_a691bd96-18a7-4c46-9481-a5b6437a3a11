Sun Aug  3 08:34:00 CEST 2025: Zastav<PERSON>jem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 08:34:06 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 50650    │ 0s     │ 0    │ online    │ 0%       │ 49.8mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 50652    │ 0s     │ 0    │ online    │ 0%       │ 39.2mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 08:34:07 CEST 2025: Spouštím medium-mobile.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 50650    │ 1s     │ 0    │ online    │ 0%       │ 96.2mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 50652    │ 1s     │ 0    │ online    │ 0%       │ 100.1mb  │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 50710    │ 0s     │ 0    │ online    │ 0%       │ 51.8mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 50711    │ 0s     │ 0    │ online    │ 0%       │ 45.1mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 08:34:08 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 50650    │ 2s     │ 0    │ online    │ 0%       │ 96.2mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 50652    │ 2s     │ 0    │ online    │ 0%       │ 100.1mb  │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 50710    │ 1s     │ 0    │ online    │ 0%       │ 98.4mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 50711    │ 1s     │ 0    │ online    │ 0%       │ 99.7mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 08:47:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 08:47:04 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 51989    │ 0s     │ 0    │ online    │ 0%       │ 33.0mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 08:47:05 CEST 2025: Spouštím medium-mobile.js s 3 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (3 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 51989    │ 0s     │ 0    │ online    │ 0%       │ 98.3mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 52057    │ 0s     │ 0    │ online    │ 0%       │ 56.7mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 52058    │ 0s     │ 0    │ online    │ 0%       │ 50.0mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 52059    │ 0s     │ 0    │ online    │ 0%       │ 38.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 08:47:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 51989    │ 1s     │ 0    │ online    │ 0%       │ 98.4mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 52057    │ 0s     │ 0    │ online    │ 0%       │ 98.4mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 52058    │ 0s     │ 0    │ online    │ 0%       │ 97.0mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 52059    │ 0s     │ 0    │ online    │ 0%       │ 99.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 08:58:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 08:58:04 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 53153    │ 0s     │ 0    │ online    │ 0%       │ 33.0mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 08:58:05 CEST 2025: Spouštím medium-mobile.js s 4 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (4 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 53153    │ 0s     │ 0    │ online    │ 0%       │ 99.1mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 53221    │ 0s     │ 0    │ online    │ 0%       │ 63.2mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 53222    │ 0s     │ 0    │ online    │ 0%       │ 57.0mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 53223    │ 0s     │ 0    │ online    │ 0%       │ 49.8mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 53224    │ 0s     │ 0    │ online    │ 0%       │ 39.9mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 08:58:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 53153    │ 1s     │ 0    │ online    │ 0%       │ 99.2mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 53221    │ 0s     │ 0    │ online    │ 0%       │ 99.6mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 53222    │ 0s     │ 0    │ online    │ 0%       │ 97.6mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 53223    │ 0s     │ 0    │ online    │ 0%       │ 101.3mb  │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 53224    │ 0s     │ 0    │ online    │ 0%       │ 100.2mb  │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:10:01 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:10:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 54597    │ 0s     │ 0    │ online    │ 0%       │ 47.4mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 54598    │ 0s     │ 0    │ online    │ 0%       │ 43.4mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:10:06 CEST 2025: Spouštím medium-mobile.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 54597    │ 1s     │ 0    │ online    │ 0%       │ 98.3mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 54598    │ 0s     │ 0    │ online    │ 0%       │ 101.2mb  │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 54657    │ 0s     │ 0    │ online    │ 0%       │ 33.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:10:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 54597    │ 1s     │ 0    │ online    │ 0%       │ 99.0mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 54598    │ 1s     │ 0    │ online    │ 0%       │ 101.3mb  │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 54657    │ 0s     │ 0    │ online    │ 0%       │ 100.6mb  │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:22:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:22:04 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 55643    │ 0s     │ 0    │ online    │ 0%       │ 47.7mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 55644    │ 0s     │ 0    │ online    │ 0%       │ 33.1mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:22:05 CEST 2025: Spouštím medium-mobile.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 55643    │ 0s     │ 0    │ online    │ 0%       │ 99.0mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 55644    │ 0s     │ 0    │ online    │ 0%       │ 98.3mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 55716    │ 0s     │ 0    │ online    │ 0%       │ 33.7mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:22:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 55643    │ 1s     │ 0    │ online    │ 0%       │ 99.5mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 55644    │ 1s     │ 0    │ online    │ 0%       │ 98.7mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 55716    │ 0s     │ 0    │ online    │ 0%       │ 95.9mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:34:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:34:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 56706    │ 0s     │ 0    │ online    │ 0%       │ 47.3mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 56707    │ 0s     │ 0    │ online    │ 0%       │ 33.0mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:34:06 CEST 2025: Spouštím medium-mobile.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 56706    │ 0s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 56707    │ 0s     │ 0    │ online    │ 0%       │ 99.1mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 56762    │ 0s     │ 0    │ online    │ 0%       │ 33.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:34:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 56706    │ 1s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 56707    │ 1s     │ 0    │ online    │ 0%       │ 99.9mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 56762    │ 0s     │ 0    │ online    │ 0%       │ 97.1mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:47:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:47:04 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 57814    │ 0s     │ 0    │ online    │ 0%       │ 49.3mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 57815    │ 0s     │ 0    │ online    │ 0%       │ 36.8mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:47:05 CEST 2025: Spouštím medium-mobile.js s 3 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (3 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 57814    │ 1s     │ 0    │ online    │ 0%       │ 99.5mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 57815    │ 0s     │ 0    │ online    │ 0%       │ 100.4mb  │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 57887    │ 0s     │ 0    │ online    │ 0%       │ 56.3mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 57888    │ 0s     │ 0    │ online    │ 0%       │ 51.2mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 57889    │ 0s     │ 0    │ online    │ 0%       │ 40.4mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:47:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 57814    │ 1s     │ 0    │ online    │ 0%       │ 100.9mb  │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 57815    │ 1s     │ 0    │ online    │ 0%       │ 100.4mb  │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 57887    │ 0s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 57888    │ 0s     │ 0    │ online    │ 0%       │ 102.0mb  │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 57889    │ 0s     │ 0    │ online    │ 0%       │ 99.4mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:58:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:58:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 59189    │ 0s     │ 0    │ online    │ 0%       │ 47.4mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 59190    │ 0s     │ 0    │ online    │ 0%       │ 42.1mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:58:06 CEST 2025: Spouštím medium-mobile.js s 3 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (3 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 59189    │ 1s     │ 0    │ online    │ 0%       │ 98.9mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 59190    │ 1s     │ 0    │ online    │ 0%       │ 98.9mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 59245    │ 0s     │ 0    │ online    │ 0%       │ 57.1mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 59246    │ 0s     │ 0    │ online    │ 0%       │ 51.1mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 59247    │ 0s     │ 0    │ online    │ 0%       │ 42.7mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 09:58:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 59189    │ 2s     │ 0    │ online    │ 0%       │ 98.9mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 59190    │ 2s     │ 0    │ online    │ 0%       │ 99.0mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 59245    │ 1s     │ 0    │ online    │ 0%       │ 99.8mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 59246    │ 1s     │ 0    │ online    │ 0%       │ 96.8mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 59247    │ 1s     │ 0    │ online    │ 0%       │ 96.7mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:10:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:10:04 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 60469    │ 0s     │ 0    │ online    │ 0%       │ 32.7mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:10:05 CEST 2025: Spouštím medium-mobile.js s 4 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (4 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 60469    │ 1s     │ 0    │ online    │ 0%       │ 96.8mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 60537    │ 0s     │ 0    │ online    │ 0%       │ 61.7mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 60538    │ 0s     │ 0    │ online    │ 0%       │ 57.1mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 60539    │ 0s     │ 0    │ online    │ 0%       │ 50.6mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 60540    │ 0s     │ 0    │ online    │ 0%       │ 39.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:10:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 60469    │ 1s     │ 0    │ online    │ 0%       │ 96.8mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 60537    │ 0s     │ 0    │ online    │ 0%       │ 98.8mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 60538    │ 0s     │ 0    │ online    │ 0%       │ 99.8mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 60539    │ 0s     │ 0    │ online    │ 0%       │ 98.7mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 60540    │ 0s     │ 0    │ online    │ 0%       │ 99.9mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:22:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium-mobile](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:22:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 61811    │ 0s     │ 0    │ online    │ 8%       │ 48.6mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 61812    │ 0s     │ 0    │ online    │ 0%       │ 36.4mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:22:06 CEST 2025: Spouštím medium-mobile.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 61811    │ 1s     │ 0    │ online    │ 0%       │ 99.8mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 61812    │ 0s     │ 0    │ online    │ 0%       │ 99.7mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 61867    │ 0s     │ 0    │ online    │ 0%       │ 48.2mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 61868    │ 0s     │ 0    │ online    │ 0%       │ 37.1mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:22:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 61811    │ 1s     │ 0    │ online    │ 0%       │ 99.8mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 61812    │ 1s     │ 0    │ online    │ 0%       │ 99.7mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 61867    │ 0s     │ 0    │ online    │ 0%       │ 98.2mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 61868    │ 0s     │ 0    │ online    │ 0%       │ 99.2mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:34:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:34:04 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 63001    │ 0s     │ 0    │ online    │ 0%       │ 50.5mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 63002    │ 0s     │ 0    │ online    │ 0%       │ 43.5mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:34:05 CEST 2025: Spouštím medium-mobile.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 63001    │ 1s     │ 0    │ online    │ 0%       │ 96.9mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 63002    │ 1s     │ 0    │ online    │ 0%       │ 99.5mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 63074    │ 0s     │ 0    │ online    │ 0%       │ 33.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:34:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 63001    │ 2s     │ 0    │ online    │ 0%       │ 97.0mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 63002    │ 1s     │ 0    │ online    │ 0%       │ 99.5mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 63074    │ 0s     │ 0    │ online    │ 0%       │ 99.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:47:01 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:47:06 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 64144    │ 0s     │ 0    │ online    │ 0%       │ 32.7mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:47:07 CEST 2025: Spouštím medium-mobile.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 64144    │ 1s     │ 0    │ online    │ 0%       │ 96.8mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 64196    │ 0s     │ 0    │ online    │ 0%       │ 49.2mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 64197    │ 0s     │ 0    │ online    │ 0%       │ 42.1mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:47:08 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 64144    │ 2s     │ 0    │ online    │ 0%       │ 96.9mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 64196    │ 1s     │ 0    │ online    │ 0%       │ 99.4mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 64197    │ 1s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:58:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium-mobile](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:58:04 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 65196    │ 0s     │ 0    │ online    │ 0%       │ 48.3mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 65197    │ 0s     │ 0    │ online    │ 0%       │ 33.8mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:58:05 CEST 2025: Spouštím medium-mobile.js s 4 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (4 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 65196    │ 1s     │ 0    │ online    │ 0%       │ 99.3mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 65197    │ 1s     │ 0    │ online    │ 0%       │ 99.0mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 65269    │ 0s     │ 0    │ online    │ 0%       │ 62.4mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 65270    │ 0s     │ 0    │ online    │ 0%       │ 57.7mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 65271    │ 0s     │ 0    │ online    │ 0%       │ 51.0mb   │ petrlzi… │ disabled │
│ 5  │ medium-mobile    │ default     │ N/A     │ cluster │ 65272    │ 0s     │ 0    │ online    │ 0%       │ 40.3mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 10:58:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 65196    │ 1s     │ 0    │ online    │ 0%       │ 99.4mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 65197    │ 1s     │ 0    │ online    │ 0%       │ 99.1mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 65269    │ 1s     │ 0    │ online    │ 0%       │ 100.3mb  │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 65270    │ 1s     │ 0    │ online    │ 0%       │ 98.1mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 65271    │ 1s     │ 0    │ online    │ 0%       │ 98.0mb   │ petrlzi… │ disabled │
│ 5  │ medium-mobile    │ default     │ N/A     │ cluster │ 65272    │ 0s     │ 0    │ online    │ 0%       │ 99.3mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:10:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4, 5 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
[PM2] [medium-mobile](5) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 5  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4, 5 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
[PM2] [medium-mobile](5) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:10:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 66677    │ 0s     │ 0    │ online    │ 8%       │ 48.3mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 66678    │ 0s     │ 0    │ online    │ 0%       │ 37.0mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:10:06 CEST 2025: Spouštím medium-mobile.js s 3 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (3 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 66677    │ 1s     │ 0    │ online    │ 0%       │ 98.6mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 66678    │ 0s     │ 0    │ online    │ 0%       │ 98.3mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 66750    │ 0s     │ 0    │ online    │ 0%       │ 56.5mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 66751    │ 0s     │ 0    │ online    │ 0%       │ 49.5mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 66752    │ 0s     │ 0    │ online    │ 0%       │ 37.6mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:10:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 66677    │ 1s     │ 0    │ online    │ 0%       │ 98.7mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 66678    │ 1s     │ 0    │ online    │ 0%       │ 98.4mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 66750    │ 0s     │ 0    │ online    │ 0%       │ 99.2mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 66751    │ 0s     │ 0    │ online    │ 0%       │ 98.1mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 66752    │ 0s     │ 0    │ online    │ 0%       │ 98.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:22:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:22:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 67938    │ 0s     │ 0    │ online    │ 0%       │ 48.3mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 67939    │ 0s     │ 0    │ online    │ 0%       │ 33.7mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:22:06 CEST 2025: Spouštím medium-mobile.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 67938    │ 0s     │ 0    │ online    │ 0%       │ 97.9mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 67939    │ 0s     │ 0    │ online    │ 0%       │ 98.8mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 67994    │ 0s     │ 0    │ online    │ 0%       │ 34.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:22:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 67938    │ 1s     │ 0    │ online    │ 0%       │ 97.9mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 67939    │ 1s     │ 0    │ online    │ 0%       │ 98.8mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 67994    │ 0s     │ 0    │ online    │ 0%       │ 98.7mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:34:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:34:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 69070    │ 0s     │ 0    │ online    │ 0%       │ 50.4mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 69071    │ 0s     │ 0    │ online    │ 0%       │ 42.6mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:34:06 CEST 2025: Spouštím medium-mobile.js s 3 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (3 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 69070    │ 1s     │ 0    │ online    │ 0%       │ 98.2mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 69071    │ 1s     │ 0    │ online    │ 0%       │ 98.3mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 69126    │ 0s     │ 0    │ online    │ 0%       │ 55.7mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 69127    │ 0s     │ 0    │ online    │ 0%       │ 50.2mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 69128    │ 0s     │ 0    │ online    │ 0%       │ 38.9mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:34:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 69070    │ 2s     │ 0    │ online    │ 0%       │ 98.3mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 69071    │ 2s     │ 0    │ online    │ 0%       │ 98.3mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 69126    │ 1s     │ 0    │ online    │ 0%       │ 97.2mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 69127    │ 1s     │ 0    │ online    │ 0%       │ 99.3mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 69128    │ 0s     │ 0    │ online    │ 0%       │ 99.0mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:47:01 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:47:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 70477    │ 0s     │ 0    │ online    │ 0%       │ 48.4mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 70478    │ 0s     │ 0    │ online    │ 0%       │ 37.1mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:47:06 CEST 2025: Spouštím medium-mobile.js s 3 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (3 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 70477    │ 1s     │ 0    │ online    │ 0%       │ 98.1mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 70478    │ 1s     │ 0    │ online    │ 0%       │ 99.3mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 70533    │ 0s     │ 0    │ online    │ 0%       │ 54.6mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 70534    │ 0s     │ 0    │ online    │ 0%       │ 48.3mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 70535    │ 0s     │ 0    │ online    │ 0%       │ 35.2mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:47:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 70477    │ 1s     │ 0    │ online    │ 0%       │ 98.2mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 70478    │ 1s     │ 0    │ online    │ 0%       │ 99.3mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 70533    │ 0s     │ 0    │ online    │ 0%       │ 97.5mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 70534    │ 0s     │ 0    │ online    │ 0%       │ 98.9mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 70535    │ 0s     │ 0    │ online    │ 0%       │ 99.0mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:58:01 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:58:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 71731    │ 0s     │ 0    │ online    │ 0%       │ 32.7mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:58:06 CEST 2025: Spouštím medium-mobile.js s 3 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (3 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 71731    │ 1s     │ 0    │ online    │ 0%       │ 98.1mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 71782    │ 0s     │ 0    │ online    │ 0%       │ 55.1mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 71783    │ 0s     │ 0    │ online    │ 0%       │ 48.2mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 71784    │ 0s     │ 0    │ online    │ 0%       │ 36.4mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 11:58:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 71731    │ 1s     │ 0    │ online    │ 0%       │ 98.1mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 71782    │ 0s     │ 0    │ online    │ 0%       │ 98.5mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 71783    │ 0s     │ 0    │ online    │ 0%       │ 99.6mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 71784    │ 0s     │ 0    │ online    │ 0%       │ 99.4mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:10:01 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:10:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 72917    │ 0s     │ 0    │ online    │ 0%       │ 32.7mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:10:06 CEST 2025: Spouštím medium-mobile.js s 4 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (4 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 72917    │ 1s     │ 0    │ online    │ 0%       │ 96.8mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 72968    │ 0s     │ 0    │ online    │ 0%       │ 63.4mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 72969    │ 0s     │ 0    │ online    │ 0%       │ 56.8mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 72970    │ 0s     │ 0    │ online    │ 0%       │ 50.0mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 72971    │ 0s     │ 0    │ online    │ 0%       │ 39.7mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:10:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 72917    │ 1s     │ 0    │ online    │ 0%       │ 96.8mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 72968    │ 0s     │ 0    │ online    │ 0%       │ 99.4mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 72969    │ 0s     │ 0    │ online    │ 0%       │ 100.1mb  │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 72970    │ 0s     │ 0    │ online    │ 0%       │ 100.2mb  │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 72971    │ 0s     │ 0    │ online    │ 0%       │ 99.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:22:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:22:04 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 74238    │ 0s     │ 0    │ online    │ 0%       │ 49.6mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 74239    │ 0s     │ 0    │ online    │ 0%       │ 39.4mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:22:05 CEST 2025: Spouštím medium-mobile.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 74238    │ 0s     │ 0    │ online    │ 0%       │ 98.7mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 74239    │ 0s     │ 0    │ online    │ 0%       │ 99.4mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 74311    │ 0s     │ 0    │ online    │ 0%       │ 48.5mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 74312    │ 0s     │ 0    │ online    │ 0%       │ 37.7mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:22:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 74238    │ 1s     │ 0    │ online    │ 0%       │ 98.8mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 74239    │ 1s     │ 0    │ online    │ 0%       │ 99.4mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 74311    │ 0s     │ 0    │ online    │ 0%       │ 98.5mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 74312    │ 0s     │ 0    │ online    │ 0%       │ 96.7mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:34:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:34:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 75459    │ 0s     │ 0    │ online    │ 0%       │ 33.1mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:34:06 CEST 2025: Spouštím medium-mobile.js s 4 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (4 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 75459    │ 1s     │ 0    │ online    │ 0%       │ 97.7mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 75510    │ 0s     │ 0    │ online    │ 0%       │ 62.2mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 75511    │ 0s     │ 0    │ online    │ 0%       │ 57.6mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 75512    │ 0s     │ 0    │ online    │ 0%       │ 51.3mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 75513    │ 0s     │ 0    │ online    │ 0%       │ 43.2mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:34:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 75459    │ 1s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 75510    │ 1s     │ 0    │ online    │ 0%       │ 99.9mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 75511    │ 0s     │ 0    │ online    │ 0%       │ 100.7mb  │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 75512    │ 0s     │ 0    │ online    │ 0%       │ 98.6mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 75513    │ 0s     │ 0    │ online    │ 0%       │ 97.4mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:47:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium-mobile](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:47:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 76815    │ 0s     │ 0    │ online    │ 0%       │ 47.4mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 76816    │ 0s     │ 0    │ online    │ 0%       │ 38.4mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:47:06 CEST 2025: Spouštím medium-mobile.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 76815    │ 1s     │ 0    │ online    │ 0%       │ 98.3mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 76816    │ 1s     │ 0    │ online    │ 0%       │ 98.9mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 76888    │ 0s     │ 0    │ online    │ 0%       │ 50.0mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 76889    │ 0s     │ 0    │ online    │ 0%       │ 37.1mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:47:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 76815    │ 2s     │ 0    │ online    │ 0%       │ 98.3mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 76816    │ 2s     │ 0    │ online    │ 0%       │ 99.0mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 76888    │ 1s     │ 0    │ online    │ 0%       │ 97.6mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 76889    │ 0s     │ 0    │ online    │ 0%       │ 99.3mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:58:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:58:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 77905    │ 0s     │ 0    │ online    │ 0%       │ 49.3mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 77906    │ 0s     │ 0    │ online    │ 0%       │ 38.8mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:58:06 CEST 2025: Spouštím medium-mobile.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 77905    │ 1s     │ 0    │ online    │ 0%       │ 96.7mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 77906    │ 1s     │ 0    │ online    │ 0%       │ 98.8mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 77978    │ 0s     │ 0    │ online    │ 0%       │ 34.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 12:58:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 77905    │ 1s     │ 0    │ online    │ 0%       │ 96.8mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 77906    │ 1s     │ 0    │ online    │ 0%       │ 98.8mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 77978    │ 0s     │ 0    │ online    │ 0%       │ 98.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:10:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:10:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 79052    │ 0s     │ 0    │ online    │ 0%       │ 48.3mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 79053    │ 0s     │ 0    │ online    │ 0%       │ 37.6mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:10:06 CEST 2025: Spouštím medium-mobile.js s 4 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (4 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 79052    │ 1s     │ 0    │ online    │ 0%       │ 99.0mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 79053    │ 0s     │ 0    │ online    │ 0%       │ 98.2mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 79125    │ 0s     │ 0    │ online    │ 0%       │ 62.4mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 79126    │ 0s     │ 0    │ online    │ 0%       │ 56.9mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 79127    │ 0s     │ 0    │ online    │ 0%       │ 50.9mb   │ petrlzi… │ disabled │
│ 5  │ medium-mobile    │ default     │ N/A     │ cluster │ 79128    │ 0s     │ 0    │ online    │ 0%       │ 40.0mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:10:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 79052    │ 1s     │ 0    │ online    │ 0%       │ 100.0mb  │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 79053    │ 1s     │ 0    │ online    │ 0%       │ 99.0mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 79125    │ 1s     │ 0    │ online    │ 0%       │ 100.9mb  │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 79126    │ 0s     │ 0    │ online    │ 0%       │ 102.0mb  │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 79127    │ 0s     │ 0    │ online    │ 0%       │ 100.2mb  │ petrlzi… │ disabled │
│ 5  │ medium-mobile    │ default     │ N/A     │ cluster │ 79128    │ 0s     │ 0    │ online    │ 0%       │ 100.5mb  │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:22:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4, 5 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
[PM2] [medium-mobile](5) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 5  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4, 5 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
[PM2] [medium-mobile](5) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:22:06 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 80509    │ 0s     │ 0    │ online    │ 0%       │ 47.5mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 80510    │ 0s     │ 0    │ online    │ 0%       │ 43.4mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:22:07 CEST 2025: Spouštím medium-mobile.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 80509    │ 1s     │ 0    │ online    │ 0%       │ 100.9mb  │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 80510    │ 1s     │ 0    │ online    │ 0%       │ 99.5mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 80565    │ 0s     │ 0    │ online    │ 0%       │ 33.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:22:08 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 80509    │ 2s     │ 0    │ online    │ 0%       │ 101.0mb  │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 80510    │ 2s     │ 0    │ online    │ 0%       │ 99.5mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 80565    │ 1s     │ 0    │ online    │ 0%       │ 98.9mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:34:01 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:34:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 81549    │ 0s     │ 0    │ online    │ 0%       │ 32.7mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:34:06 CEST 2025: Spouštím medium-mobile.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 81549    │ 0s     │ 0    │ online    │ 0%       │ 98.5mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 81617    │ 0s     │ 0    │ online    │ 0%       │ 33.7mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:34:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 81549    │ 1s     │ 0    │ online    │ 0%       │ 98.6mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 81617    │ 0s     │ 0    │ online    │ 0%       │ 98.0mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:47:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:47:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 82539    │ 0s     │ 0    │ online    │ 0%       │ 32.7mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:47:06 CEST 2025: Spouštím medium-mobile.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 82539    │ 0s     │ 0    │ online    │ 0%       │ 96.0mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 82607    │ 0s     │ 0    │ online    │ 0%       │ 48.4mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 82608    │ 0s     │ 0    │ online    │ 0%       │ 38.2mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:47:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 82539    │ 1s     │ 0    │ online    │ 0%       │ 96.0mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 82607    │ 0s     │ 0    │ online    │ 0%       │ 98.2mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 82608    │ 0s     │ 0    │ online    │ 0%       │ 98.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:58:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:58:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 83544    │ 0s     │ 0    │ online    │ 0%       │ 48.0mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 83545    │ 0s     │ 0    │ online    │ 0%       │ 36.0mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:58:06 CEST 2025: Spouštím medium-mobile.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 83544    │ 0s     │ 0    │ online    │ 0%       │ 98.3mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 83545    │ 0s     │ 0    │ online    │ 0%       │ 97.7mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 83617    │ 0s     │ 0    │ online    │ 0%       │ 48.7mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 83618    │ 0s     │ 0    │ online    │ 0%       │ 37.3mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 13:58:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 83544    │ 1s     │ 0    │ online    │ 0%       │ 98.4mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 83545    │ 1s     │ 0    │ online    │ 0%       │ 98.0mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 83617    │ 0s     │ 0    │ online    │ 0%       │ 97.3mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 83618    │ 0s     │ 0    │ online    │ 0%       │ 97.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:10:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:10:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 84707    │ 0s     │ 0    │ online    │ 0%       │ 32.8mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:10:06 CEST 2025: Spouštím medium-mobile.js s 3 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (3 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 84707    │ 1s     │ 0    │ online    │ 0%       │ 98.5mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 84775    │ 0s     │ 0    │ online    │ 0%       │ 55.7mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 84776    │ 0s     │ 0    │ online    │ 0%       │ 51.1mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 84777    │ 0s     │ 0    │ online    │ 0%       │ 39.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:10:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 84707    │ 1s     │ 0    │ online    │ 0%       │ 98.6mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 84775    │ 0s     │ 0    │ online    │ 0%       │ 98.7mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 84776    │ 0s     │ 0    │ online    │ 0%       │ 98.7mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 84777    │ 0s     │ 0    │ online    │ 0%       │ 98.3mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:22:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:22:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 85922    │ 0s     │ 0    │ online    │ 0%       │ 32.5mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:22:06 CEST 2025: Spouštím medium-mobile.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 85922    │ 0s     │ 0    │ online    │ 0%       │ 98.8mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 85990    │ 0s     │ 0    │ online    │ 0%       │ 50.2mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 85991    │ 0s     │ 0    │ online    │ 0%       │ 39.7mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:22:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 85922    │ 1s     │ 0    │ online    │ 0%       │ 98.8mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 85990    │ 0s     │ 0    │ online    │ 0%       │ 97.7mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 85991    │ 0s     │ 0    │ online    │ 0%       │ 99.2mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:34:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:34:04 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 86975    │ 0s     │ 0    │ online    │ 0%       │ 47.4mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 86976    │ 0s     │ 0    │ online    │ 0%       │ 33.1mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:34:05 CEST 2025: Spouštím medium-mobile.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 86975    │ 0s     │ 0    │ online    │ 0%       │ 98.4mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 86976    │ 0s     │ 0    │ online    │ 0%       │ 98.0mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 87031    │ 0s     │ 0    │ online    │ 0%       │ 48.5mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 87032    │ 0s     │ 0    │ online    │ 0%       │ 34.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:34:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 86975    │ 1s     │ 0    │ online    │ 0%       │ 98.5mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 86976    │ 1s     │ 0    │ online    │ 0%       │ 98.1mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 87031    │ 0s     │ 0    │ online    │ 0%       │ 98.4mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 87032    │ 0s     │ 0    │ online    │ 0%       │ 98.9mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:47:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:47:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 88237    │ 0s     │ 0    │ online    │ 0%       │ 48.2mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 88238    │ 0s     │ 0    │ online    │ 0%       │ 37.6mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:47:06 CEST 2025: Spouštím medium-mobile.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 88237    │ 1s     │ 0    │ online    │ 0%       │ 98.1mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 88238    │ 1s     │ 0    │ online    │ 0%       │ 98.5mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 88310    │ 0s     │ 0    │ online    │ 0%       │ 32.9mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:47:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 88237    │ 1s     │ 0    │ online    │ 0%       │ 98.1mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 88238    │ 1s     │ 0    │ online    │ 0%       │ 98.6mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 88310    │ 0s     │ 0    │ online    │ 0%       │ 96.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:58:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:58:04 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 89276    │ 0s     │ 0    │ online    │ 0%       │ 33.0mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:58:05 CEST 2025: Spouštím medium-mobile.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 89276    │ 0s     │ 0    │ online    │ 0%       │ 98.5mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 89327    │ 0s     │ 0    │ online    │ 0%       │ 32.9mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 14:58:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 89276    │ 1s     │ 0    │ online    │ 0%       │ 98.6mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 89327    │ 0s     │ 0    │ online    │ 0%       │ 98.7mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:10:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:10:04 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 90209    │ 0s     │ 0    │ online    │ 0%       │ 32.6mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:10:05 CEST 2025: Spouštím medium-mobile.js s 4 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (4 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 90209    │ 0s     │ 0    │ online    │ 0%       │ 99.0mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 90260    │ 0s     │ 0    │ online    │ 0%       │ 61.5mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 90261    │ 0s     │ 0    │ online    │ 0%       │ 57.6mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 90262    │ 0s     │ 0    │ online    │ 0%       │ 50.7mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 90263    │ 0s     │ 0    │ online    │ 0%       │ 40.1mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:10:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 90209    │ 1s     │ 0    │ online    │ 0%       │ 99.0mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 90260    │ 0s     │ 0    │ online    │ 0%       │ 99.8mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 90261    │ 0s     │ 0    │ online    │ 0%       │ 99.9mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 90262    │ 0s     │ 0    │ online    │ 0%       │ 100.1mb  │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 90263    │ 0s     │ 0    │ online    │ 0%       │ 99.9mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:22:01 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:22:06 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 91536    │ 0s     │ 0    │ online    │ 7%       │ 46.6mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 91537    │ 0s     │ 0    │ online    │ 3%       │ 36.4mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:22:07 CEST 2025: Spouštím medium-mobile.js s 4 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (4 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 91536    │ 1s     │ 0    │ online    │ 0%       │ 100.8mb  │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 91537    │ 1s     │ 0    │ online    │ 0%       │ 100.5mb  │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 91592    │ 0s     │ 0    │ online    │ 0%       │ 62.5mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 91593    │ 0s     │ 0    │ online    │ 0%       │ 56.1mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 91594    │ 0s     │ 0    │ online    │ 0%       │ 49.8mb   │ petrlzi… │ disabled │
│ 5  │ medium-mobile    │ default     │ N/A     │ cluster │ 91595    │ 0s     │ 0    │ online    │ 0%       │ 37.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:22:08 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 91536    │ 1s     │ 0    │ online    │ 0%       │ 100.8mb  │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 91537    │ 1s     │ 0    │ online    │ 0%       │ 100.6mb  │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 91592    │ 0s     │ 0    │ online    │ 0%       │ 97.4mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 91593    │ 0s     │ 0    │ online    │ 0%       │ 98.7mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 91594    │ 0s     │ 0    │ online    │ 0%       │ 100.5mb  │ petrlzi… │ disabled │
│ 5  │ medium-mobile    │ default     │ N/A     │ cluster │ 91595    │ 0s     │ 0    │ online    │ 0%       │ 102.0mb  │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:34:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4, 5 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](4) ✓
[PM2] [medium-mobile](5) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 5  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4, 5 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
[PM2] [medium-mobile](5) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:34:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 92930    │ 0s     │ 0    │ online    │ 0%       │ 33.2mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:34:06 CEST 2025: Spouštím medium-mobile.js s 3 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (3 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 92930    │ 1s     │ 0    │ online    │ 1%       │ 98.2mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 92981    │ 0s     │ 0    │ online    │ 6%       │ 57.8mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 92982    │ 0s     │ 0    │ online    │ 6%       │ 51.1mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 93000    │ 0s     │ 0    │ online    │ 4%       │ 40.3mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:34:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 92930    │ 1s     │ 0    │ online    │ 0%       │ 98.2mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 92981    │ 1s     │ 0    │ online    │ 0%       │ 98.5mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 92982    │ 0s     │ 0    │ online    │ 0%       │ 100.6mb  │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 93000    │ 0s     │ 0    │ online    │ 0%       │ 98.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:47:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium-mobile](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:47:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 94217    │ 0s     │ 0    │ online    │ 0%       │ 32.8mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:47:06 CEST 2025: Spouštím medium-mobile.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 94217    │ 1s     │ 0    │ online    │ 0%       │ 98.4mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 94285    │ 0s     │ 0    │ online    │ 0%       │ 49.9mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 94286    │ 0s     │ 0    │ online    │ 0%       │ 42.7mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:47:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 94217    │ 1s     │ 0    │ online    │ 0%       │ 98.4mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 94285    │ 0s     │ 0    │ online    │ 0%       │ 99.1mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 94286    │ 0s     │ 0    │ online    │ 0%       │ 100.0mb  │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:58:01 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:58:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 95284    │ 0s     │ 0    │ online    │ 0%       │ 47.2mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 95285    │ 0s     │ 0    │ online    │ 0%       │ 36.7mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:58:06 CEST 2025: Spouštím medium-mobile.js s 3 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (3 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 95284    │ 1s     │ 0    │ online    │ 0%       │ 96.9mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 95285    │ 0s     │ 0    │ online    │ 0%       │ 98.9mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 95357    │ 0s     │ 0    │ online    │ 0%       │ 56.3mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 95358    │ 0s     │ 0    │ online    │ 0%       │ 51.3mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 95359    │ 0s     │ 0    │ online    │ 0%       │ 41.0mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 15:58:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 95284    │ 1s     │ 0    │ online    │ 0%       │ 97.4mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 95285    │ 1s     │ 0    │ online    │ 0%       │ 99.0mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 95357    │ 0s     │ 0    │ online    │ 0%       │ 98.9mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 95358    │ 0s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 95359    │ 0s     │ 0    │ online    │ 0%       │ 99.0mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:10:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:10:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 96616    │ 0s     │ 0    │ online    │ 0%       │ 47.4mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 96617    │ 0s     │ 0    │ online    │ 0%       │ 36.3mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:10:06 CEST 2025: Spouštím medium-mobile.js s 3 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (3 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 96616    │ 1s     │ 0    │ online    │ 0%       │ 100.4mb  │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 96617    │ 0s     │ 0    │ online    │ 0%       │ 98.9mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 96672    │ 0s     │ 0    │ online    │ 0%       │ 56.0mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 96674    │ 0s     │ 0    │ online    │ 0%       │ 51.0mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 96692    │ 0s     │ 0    │ online    │ 0%       │ 41.6mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:10:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 96616    │ 1s     │ 0    │ online    │ 0%       │ 100.4mb  │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 96617    │ 1s     │ 0    │ online    │ 0%       │ 99.5mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 96672    │ 0s     │ 0    │ online    │ 0%       │ 98.9mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 96674    │ 0s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 96692    │ 0s     │ 0    │ online    │ 0%       │ 98.2mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:22:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:22:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 97931    │ 0s     │ 0    │ online    │ 0%       │ 32.6mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:22:06 CEST 2025: Spouštím medium-mobile.js s 4 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (4 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 97931    │ 1s     │ 0    │ online    │ 0%       │ 96.2mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 97999    │ 0s     │ 0    │ online    │ 0%       │ 61.8mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 98000    │ 0s     │ 0    │ online    │ 0%       │ 57.1mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 98001    │ 0s     │ 0    │ online    │ 0%       │ 50.1mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 98002    │ 0s     │ 0    │ online    │ 0%       │ 39.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:22:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 97931    │ 1s     │ 0    │ online    │ 0%       │ 96.2mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 97999    │ 0s     │ 0    │ online    │ 0%       │ 99.1mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 98000    │ 0s     │ 0    │ online    │ 0%       │ 99.3mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 98001    │ 0s     │ 0    │ online    │ 0%       │ 99.5mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 98002    │ 0s     │ 0    │ online    │ 0%       │ 97.9mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:34:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium-mobile](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:34:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 99351    │ 0s     │ 0    │ online    │ 0%       │ 48.4mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 99352    │ 0s     │ 0    │ online    │ 0%       │ 36.3mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:34:06 CEST 2025: Spouštím medium-mobile.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 99351    │ 0s     │ 0    │ online    │ 0%       │ 98.5mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 99352    │ 0s     │ 0    │ online    │ 0%       │ 99.5mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 99424    │ 0s     │ 0    │ online    │ 0%       │ 51.8mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 99425    │ 0s     │ 0    │ online    │ 0%       │ 43.4mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:34:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 99351    │ 1s     │ 0    │ online    │ 0%       │ 98.6mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 99352    │ 1s     │ 0    │ online    │ 0%       │ 99.5mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 99424    │ 0s     │ 0    │ online    │ 0%       │ 97.7mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 99425    │ 0s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:47:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:47:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 1258     │ 0s     │ 0    │ online    │ 0%       │ 48.9mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 1259     │ 0s     │ 0    │ online    │ 0%       │ 39.0mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:47:06 CEST 2025: Spouštím medium-mobile.js s 3 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (3 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 1258     │ 1s     │ 0    │ online    │ 0%       │ 98.8mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 1259     │ 1s     │ 0    │ online    │ 0%       │ 99.4mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 1320     │ 0s     │ 0    │ online    │ 0%       │ 56.7mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 1321     │ 0s     │ 0    │ online    │ 0%       │ 51.2mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 1322     │ 0s     │ 0    │ online    │ 0%       │ 41.3mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:47:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 1258     │ 1s     │ 0    │ online    │ 0%       │ 98.8mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 1259     │ 1s     │ 0    │ online    │ 0%       │ 99.5mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 1320     │ 1s     │ 0    │ online    │ 0%       │ 100.9mb  │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 1321     │ 0s     │ 0    │ online    │ 0%       │ 99.1mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 1322     │ 0s     │ 0    │ online    │ 0%       │ 100.7mb  │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:58:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:58:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 2587     │ 0s     │ 0    │ online    │ 5%       │ 32.9mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:58:06 CEST 2025: Spouštím medium-mobile.js s 4 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (4 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 2587     │ 1s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 2638     │ 0s     │ 0    │ online    │ 0%       │ 61.8mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 2639     │ 0s     │ 0    │ online    │ 0%       │ 57.4mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 2640     │ 0s     │ 0    │ online    │ 0%       │ 51.2mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 2641     │ 0s     │ 0    │ online    │ 0%       │ 43.9mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 16:58:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 2587     │ 2s     │ 0    │ online    │ 0%       │ 97.9mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 2638     │ 1s     │ 0    │ online    │ 0%       │ 101.0mb  │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 2639     │ 0s     │ 0    │ online    │ 0%       │ 98.1mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 2640     │ 0s     │ 0    │ online    │ 0%       │ 99.2mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 2641     │ 0s     │ 0    │ online    │ 0%       │ 99.9mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:10:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:10:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 3971     │ 0s     │ 0    │ online    │ 0%       │ 32.7mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:10:06 CEST 2025: Spouštím medium-mobile.js s 4 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (4 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 3971     │ 0s     │ 0    │ online    │ 0%       │ 97.7mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 4022     │ 0s     │ 0    │ online    │ 11%      │ 62.9mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 4023     │ 0s     │ 0    │ online    │ 8%       │ 57.7mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 4024     │ 0s     │ 0    │ online    │ 0%       │ 52.2mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 4025     │ 0s     │ 0    │ online    │ 0%       │ 44.4mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:10:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 3971     │ 1s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 4022     │ 0s     │ 0    │ online    │ 0%       │ 99.6mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 4023     │ 0s     │ 0    │ online    │ 0%       │ 100.8mb  │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 4024     │ 0s     │ 0    │ online    │ 0%       │ 100.3mb  │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 4025     │ 0s     │ 0    │ online    │ 0%       │ 99.9mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:22:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:22:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 5305     │ 0s     │ 0    │ online    │ 0%       │ 32.8mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:22:06 CEST 2025: Spouštím medium-mobile.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 5305     │ 0s     │ 0    │ online    │ 0%       │ 99.1mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 5356     │ 0s     │ 0    │ online    │ 0%       │ 33.2mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:22:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 5305     │ 1s     │ 0    │ online    │ 0%       │ 99.3mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 5356     │ 0s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:34:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:34:04 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 6261     │ 0s     │ 0    │ online    │ 0%       │ 32.7mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:34:05 CEST 2025: Spouštím medium-mobile.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 6261     │ 1s     │ 0    │ online    │ 0%       │ 97.4mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 6312     │ 0s     │ 0    │ online    │ 0%       │ 49.5mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 6313     │ 0s     │ 0    │ online    │ 0%       │ 39.3mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:34:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 6261     │ 1s     │ 0    │ online    │ 0%       │ 97.4mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 6312     │ 0s     │ 0    │ online    │ 0%       │ 98.6mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 6313     │ 0s     │ 0    │ online    │ 0%       │ 99.3mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:47:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:47:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 7360     │ 0s     │ 0    │ online    │ 0%       │ 32.8mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:47:06 CEST 2025: Spouštím medium-mobile.js s 3 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (3 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 7360     │ 0s     │ 0    │ online    │ 0%       │ 97.7mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 7411     │ 0s     │ 0    │ online    │ 0%       │ 56.5mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 7412     │ 0s     │ 0    │ online    │ 0%       │ 50.5mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 7413     │ 0s     │ 0    │ online    │ 0%       │ 44.4mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:47:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 7360     │ 1s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 7411     │ 0s     │ 0    │ online    │ 0%       │ 98.1mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 7412     │ 0s     │ 0    │ online    │ 0%       │ 98.4mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 7413     │ 0s     │ 0    │ online    │ 0%       │ 98.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:58:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:58:04 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 8507     │ 0s     │ 0    │ online    │ 0%       │ 49.4mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 8508     │ 0s     │ 0    │ online    │ 0%       │ 37.7mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:58:05 CEST 2025: Spouštím medium-mobile.js s 3 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (3 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 8507     │ 1s     │ 0    │ online    │ 0%       │ 97.9mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 8508     │ 0s     │ 0    │ online    │ 0%       │ 99.8mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 8563     │ 0s     │ 0    │ online    │ 0%       │ 56.4mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 8564     │ 0s     │ 0    │ online    │ 0%       │ 51.3mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 8565     │ 0s     │ 0    │ online    │ 0%       │ 44.5mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 17:58:06 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 8507     │ 1s     │ 0    │ online    │ 0%       │ 97.9mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 8508     │ 1s     │ 0    │ online    │ 0%       │ 99.8mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 8563     │ 0s     │ 0    │ online    │ 0%       │ 98.2mb   │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 8564     │ 0s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 8565     │ 0s     │ 0    │ online    │ 0%       │ 98.1mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:10:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 3  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 4  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2, 3, 4 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
[PM2] [medium-mobile](3) ✓
[PM2] [medium-mobile](4) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:10:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 9849     │ 0s     │ 0    │ online    │ 0%       │ 47.6mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 9850     │ 0s     │ 0    │ online    │ 0%       │ 35.7mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:10:06 CEST 2025: Spouštím medium-mobile.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 9849     │ 0s     │ 0    │ online    │ 0%       │ 100.0mb  │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 9850     │ 0s     │ 0    │ online    │ 0%       │ 99.7mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 9905     │ 0s     │ 0    │ online    │ 0%       │ 33.3mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:10:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 9849     │ 1s     │ 0    │ online    │ 0%       │ 100.1mb  │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 9850     │ 1s     │ 0    │ online    │ 0%       │ 100.7mb  │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 9905     │ 0s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:22:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:22:05 CEST 2025: Spouštím medium.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 11001    │ 0s     │ 0    │ online    │ 0%       │ 48.0mb   │ petrlzi… │ disabled │
│ 1  │ medium    │ default     │ N/A     │ cluster │ 11002    │ 0s     │ 0    │ online    │ 0%       │ 44.9mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:22:06 CEST 2025: Spouštím medium-mobile.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 11001    │ 1s     │ 0    │ online    │ 0%       │ 99.9mb   │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 11002    │ 1s     │ 0    │ online    │ 0%       │ 99.8mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 11057    │ 0s     │ 0    │ online    │ 0%       │ 32.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:22:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 11001    │ 2s     │ 0    │ online    │ 0%       │ 100.3mb  │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 11002    │ 2s     │ 0    │ online    │ 0%       │ 99.9mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 11057    │ 1s     │ 0    │ online    │ 0%       │ 97.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:34:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:34:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 12099    │ 0s     │ 0    │ online    │ 0%       │ 32.5mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:34:06 CEST 2025: Spouštím medium-mobile.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 12099    │ 1s     │ 0    │ online    │ 0%       │ 98.2mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 12150    │ 0s     │ 0    │ online    │ 0%       │ 49.2mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 12151    │ 0s     │ 0    │ online    │ 0%       │ 38.7mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:34:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 12099    │ 2s     │ 0    │ online    │ 0%       │ 98.2mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 12150    │ 1s     │ 0    │ online    │ 0%       │ 100.2mb  │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 12151    │ 1s     │ 0    │ online    │ 0%       │ 99.2mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:47:00 CEST 2025: Zastavujem a mažem všechny PM2 procesy...
[PM2] Applying action stopProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium-mobile](1) ✓
[PM2] [medium](0) ✓
[PM2] [medium-mobile](2) ✓
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 0        │ 0      │ 0    │ stopped   │ 0%       │ 0b       │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
[PM2] Applying action deleteProcessId on app [all](ids: [ 0, 1, 2 ])
[PM2] [medium](0) ✓
[PM2] [medium-mobile](1) ✓
[PM2] [medium-mobile](2) ✓
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:47:05 CEST 2025: Spouštím medium.js s 1 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium.js in cluster_mode (1 instance)
[PM2] Done.
┌────┬───────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium    │ default     │ N/A     │ cluster │ 13321    │ 0s     │ 0    │ online    │ 0%       │ 32.9mb   │ petrlzi… │ disabled │
└────┴───────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:47:06 CEST 2025: Spouštím medium-mobile.js s 2 instancemi...
[PM2] Starting /Users/<USER>/code/medium-crawler/medium-mobile.js in cluster_mode (2 instances)
[PM2] Done.
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 13321    │ 1s     │ 0    │ online    │ 0%       │ 99.7mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 13389    │ 0s     │ 0    │ online    │ 0%       │ 50.6mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 13390    │ 0s     │ 0    │ online    │ 0%       │ 43.8mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
Sun Aug  3 18:47:07 CEST 2025: Restart dokončen
┌────┬──────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name             │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼──────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ medium           │ default     │ N/A     │ cluster │ 13321    │ 2s     │ 0    │ online    │ 0%       │ 99.7mb   │ petrlzi… │ disabled │
│ 1  │ medium-mobile    │ default     │ N/A     │ cluster │ 13389    │ 1s     │ 0    │ online    │ 0%       │ 98.4mb   │ petrlzi… │ disabled │
│ 2  │ medium-mobile    │ default     │ N/A     │ cluster │ 13390    │ 1s     │ 0    │ online    │ 0%       │ 98.9mb   │ petrlzi… │ disabled │
└────┴──────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
