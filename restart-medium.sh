#!/bin/bash

# Nastavení PATH pro npx
export PATH="/usr/local/bin:/usr/bin:/bin:$PATH"

# Nebo použijte plnou cestu k npx (najděte pomocí: which npx)
# NPX_PATH=$(which npx)

# Zastavení a smazání všech PM2 procesů
echo "$(date): Zastavujem a mažem všechny PM2 procesy..."
npx pm2 stop all
npx pm2 delete all

# Krátké čekání
sleep 2

# Náhodný počet instancí pro medium.js (1 nebo 2)
MEDIUM_INSTANCES=$((RANDOM % 2 + 1))

# Náhodný počet instancí pro medium-mobile.js (1-4)
MOBILE_INSTANCES=$((RANDOM % 4 + 1))
# Náhodný počet instancí pro medium-mobile.js (1-4)
#MOBILE_INSTANCES=$((RANDOM % 2 + 1))

echo "$(date): Spouštím medium.js s $MEDIUM_INSTANCES instancemi..."
npx pm2 start medium.js --name "medium" -i $MEDIUM_INSTANCES

echo "$(date): Spouštím medium-mobile.js s $MOBILE_INSTANCES instancemi..."
npx pm2 start medium-mobile.js --name "medium-mobile" -i $MOBILE_INSTANCES

echo "$(date): Restart dokončen"
npx pm2 ls