const { chromium } = require('playwright');
const { mediumUrls } = require('./medium-urls');

(async () => {
    // Seznam URL adres importován z medium-urls.js
    const urls = mediumUrls;

    // Proměnná pro sledování poslední použité URL
    let lastUsedUrlIndex = -1;

    // Seznam různých user agentů pro simulaci desktop zařízení (PC, Mac, Linux)
    const userAgents = [
        // Windows Chrome
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        // Windows Firefox
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        // Windows Edge
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
        // Windows 11 Chrome
        'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        // macOS Safari
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        // macOS Chrome
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        // macOS Firefox
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0',
        // Linux Chrome
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        // Linux Firefox
        'Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0',
        // Linux Ubuntu Chrome
        'Mozilla/5.0 (X11; Ubuntu; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36'
    ];

    console.log('Spouštím instanci prohlížeče na pozadí...');
    // Spuštění prohlížeče v headless režimu - pouze jednou, mimo smyčku
    const browser = await chromium.launch({ headless: false });

    // Nekonečná smyčka - celý proces se opakuje
    while (true) {
        let context = null;

        try {
            console.log(`Vytvářím nový kontext prohlížeče... [${new Date().toLocaleString('cs-CZ')}]`);

            // Náhodný výběr user agenta
            const randomUserAgent = userAgents[Math.floor(Math.random() * userAgents.length)];
            console.log(`Vybraný User Agent: ${randomUserAgent}`);

            // Vytvoření nového kontextu s vlastním user agentem
            context = await browser.newContext({
                userAgent: randomUserAgent
            });
            const page = await context.newPage();

            // Ověření nastaveného user agenta
            const actualUserAgent = await page.evaluate(() => navigator.userAgent);
            console.log(`Aktuální User Agent: ${actualUserAgent}`);

            // Náhodný výběr URL ze seznamu
            let randomIndex;
            do {
                // Generování náhodného indexu v rozsahu pole URLs
                randomIndex = Math.floor(Math.random() * urls.length);
                // Pokračujeme v generování, dokud nedostaneme jiný index než naposledy (pokud je více než 1 URL)
            } while (urls.length > 1 && randomIndex === lastUsedUrlIndex);

            // Uložení aktuálního indexu pro příští kontrolu
            lastUsedUrlIndex = randomIndex;
            const currentUrl = urls[randomIndex];
            console.log(`Vybrána URL s indexem ${randomIndex} z ${urls.length} dostupných`);

            // Otevření URL
            console.log(`Otevírám stránku: ${currentUrl}`);
            await page.goto(currentUrl, { waitUntil: 'networkidle', timeout: 60000 });
            console.log('Stránka načtena');

            // Počkáme chvíli, aby se dialog mohl zobrazit
            console.log('Čekám na načtení dialogu...');
            await page.waitForTimeout(3000);

            // Zkontrolujeme, zda existují iframe na stránce
            const frames = page.frames();
            console.log(`Počet iframe na stránce: ${frames.length}`);

            // Zkusíme najít iframe s dialogem souhlasu
            let consentFrame = null;
            for (const frame of frames) {
                const frameUrl = frame.url();
                if (frameUrl.includes('cmp.seznam.cz') || frameUrl.includes('consent') || frameUrl.includes('gdpr')) {
                    console.log(`Nalezen iframe s dialogem souhlasu: ${frameUrl}`);
                    consentFrame = frame;
                    break;
                }
            }

            // Pokud jsme našli iframe s dialogem, zkusíme v něm najít tlačítko
            if (consentFrame) {
                console.log('Hledám tlačítko souhlasu v iframe...');

                // Zkusíme najít tlačítko souhlasu podle selektoru
                try {
                    const agreeButton = consentFrame.locator('button[data-testid="cw-button-agree-with-ads"]');
                    const count = await agreeButton.count();

                    if (count > 0) {
                        const buttonText = await agreeButton.textContent();
                        console.log(`Nalezeno tlačítko souhlasu: ${buttonText}`);
                        await agreeButton.click();
                        console.log('Kliknutí na tlačítko Souhlasím proběhlo úspěšně.');
                    } else {
                        console.log('Tlačítko souhlasu nenalezeno podle selektoru.');
                    }
                } catch (err) {
                    console.log('Chyba při hledání tlačítka souhlasu:', err.message);
                }
            } else {
                console.log('Iframe s dialogem souhlasu nenalezen.');
            }

            // Náhodný scroll po stránce
            try {
                // Krátké čekání před scrollováním
                const preScrollWait = Math.floor(Math.random() * 2000) + 2000; // 2-4 sekundy
                console.log(`Čekám ${preScrollWait}ms před scrollováním...`);
                await page.waitForTimeout(preScrollWait);

                console.log('Provádím náhodný scroll po stránce...');
                const scrollCount = Math.floor(Math.random() * 5) + 2; // 1-4 scrolly

                for (let i = 0; i < scrollCount; i++) {
                    const scrollDirection = Math.random() > 0.8 ? -1 : 1; // 80% dolů, 20% nahoru
                    const scrollDistance = Math.floor(Math.random() * 800) + 200; // 200-1000px

                    await page.evaluate(({ distance, direction }) => {
                        window.scrollBy(0, distance * direction);
                    }, { distance: scrollDistance, direction: scrollDirection });

                    // Pauza mezi scrolly 1-2 sekundy
                    if (i < scrollCount - 1) { // Nepauza po posledním scrollu
                        const pauseBetweenScrolls = Math.floor(Math.random() * 1000) + 1000; // 1000-2000ms (1-2s)
                        await page.waitForTimeout(pauseBetweenScrolls);
                    }
                }

                console.log(`Provedeno ${scrollCount} náhodných scrollů`);
            } catch (scrollError) {
                console.log('Chyba při scrollování:', scrollError.message);
            }

            // Počkáme 3 sekundy po kliknutí na tlačítko
            // const minimumWaitingTime = 15000;
            // const maximumWaitingTime = 30000;
            const minimumWaitingTime = 1500;
            const maximumWaitingTime = 3500;
            const waitTime = Math.floor(Math.random() * (maximumWaitingTime - minimumWaitingTime + 1)) + minimumWaitingTime;
            console.log(`Čekám ${waitTime} sekundy po kliknutí...`);
            await page.waitForTimeout(waitTime);

            // Pokus o kliknutí na reklamu
            try {
                console.log('Hledám reklamu pro kliknutí...');
                // Najdeme element s třídou "c_kR" a jeho druhé dítě (index 1)
                let adElement = page.locator('.c_k0').nth(0).locator('> *').nth(1);
                let adCount = await adElement.count();
                //fallback na jiny locator
                if (adCount == 0) {
                    adElement = page.locator('.d_ht').nth(0).locator('> *').nth(1);
                }
                adCount = await adElement.count();
                //fallback na jiny locator
                if (adCount == 0) {
                    adElement = page.locator('.c_jn').nth(0).locator('> *').nth(1);
                }


                adCount = await adElement.count();

                if (adCount > 0) {
                    // Kontrola, zda je reklama v zorném poli
                    const isVisible = await adElement.isVisible();
                    const isInViewport = await adElement.evaluate(element => {
                        const rect = element.getBoundingClientRect();
                        return (
                            rect.top >= 0 &&
                            rect.left >= 0 &&
                            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
                        );
                    });

                    console.log(`Reklama nalezena - viditelná: ${isVisible}, v zorném poli: ${isInViewport}`);

                    if (isVisible && isInViewport) {
                        console.log('Reklama je v zorném poli, nic nedelam...');
                        // await adElement.click();
                        // console.log('Klik na reklamu byl úspěšný.');
                    } else {
                        console.log('Reklama není v zorném poli, scrolluji k ní...');
                        await adElement.scrollIntoViewIfNeeded();
                        await page.waitForTimeout(1000); // Krátké čekání po scrollu

                        // Znovu kontrola po scrollu
                        // const isVisibleAfterScroll = await adElement.isVisible();
                        // if (isVisibleAfterScroll) {
                        //     console.log('Po scrollu je reklama viditelná, provádím klik...');
                        //     await adElement.click();
                        //     console.log('Klik na reklamu byl úspěšný.');
                        // } else {
                        //     console.log('Reklama není viditelná ani po scrollu.');
                        //     return; // Ukončíme pokus o klik
                        // }
                    }

                    // Krátké čekání po kliknutí na reklamu
                    await page.waitForTimeout(3000);

                    // Druhý klik se provede pouze v 1 případě ze 3 (33% pravděpodobnost)
                    // const shouldClickAgain = Math.random() < 0.33;
                    const shouldClickAgain = true;//Math.random() < 0.33;
                    if (shouldClickAgain) {
                        console.log('Provádím klik na reklamu...');
                        await adElement.click();
                        console.log('Klik na reklamu byl úspěšný  - reklama Otevřena.');
                        const randomWaitTime = Math.floor(Math.random() * (15000 - 5000 + 1)) + 5000;
                        console.log(`Čekám ${randomWaitTime}ms po druhém kliku na reklamu...`);
                        await page.waitForTimeout(randomWaitTime);
                    } else {
                        console.log('Druhý klik na reklamu přeskočen (1 ze 3 pokusů).');
                        await page.waitForTimeout(1000);
                        await context.close();
                    }


                } else {
                    console.log('Reklama nebyla nalezena.');
                }
            } catch (adError) {
                console.log('Chyba při klikání na reklamu:', adError.message);
            }

            await page.waitForTimeout(90000);
        } catch (error) {
            console.error('Došlo k chybě:', error);
        } finally {
            // Místo zavření prohlížeče pouze zavřeme kontext (vymaže cookies a data)
            if (context) {
                console.log('Zavírám kontext a mažu cookies...');
                await context.close();
                console.log('Kontext zavřen, cookies vymazány');
            }

            console.log('Začínám nový cyklus...');
            console.log('-----------------------------------');
        }
    }

    // Tato část se pravděpodobně nikdy neprovede, protože smyčka je nekonečná
    // Ale pro úplnost přidáváme zavření prohlížeče
    await browser.close();
})();
